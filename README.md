# 🏥 Medical Voice Assistant

A comprehensive medical voice application with two main modes:
1. **Voice-to-Text Analysis** - Real-time transcription, speaker identification, and AI summaries
2. **Pure Voice Recording** - Simple audio recording and MP3 generation

## 🚀 Quick Start

### Option 1: Use the Runner Script (Recommended)
```bash
python run_app.py
```
This will show you a menu to start different parts of the application.

### Option 2: Manual Commands

#### 1. Start Backend Server
```bash
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 2. Start Frontend Applications

**Main Page (Navigation Hub):**
```bash
cd frontend
streamlit run main_page.py --server.port 8501
```

**Voice-to-Text App:**
```bash
cd frontend
streamlit run streamlit_app.py --server.port 8502
```

**Voice Recorder App:**
```bash
cd frontend
streamlit run voice_recorder.py --server.port 8503
```

## 📁 Project Structure

```
Baby/
├── backend/
│   ├── main.py                    # FastAPI routes only
│   ├── voice_to_text_service.py   # Voice-to-text logic
│   ├── voice_recording_service.py # Pure voice recording logic
│   ├── groq_whisper.py           # Whisper API integration
│   ├── requirements.txt          # Backend dependencies
│   └── recordings/               # Saved MP3 files
├── frontend/
│   ├── main_page.py              # Main navigation page
│   ├── streamlit_app.py          # Voice-to-text interface
│   └── voice_recorder.py         # Voice recording interface
├── run_app.py                    # Application runner script
└── README.md                     # This file
```

## 🔧 Installation

1. **Install Dependencies:**
   ```bash
   python run_app.py
   # Choose option 5 to install dependencies
   ```

2. **Set up Environment Variables:**
   Create a `.env` file in the `backend/` directory:
   ```
   GROQ_API_KEY=your_groq_api_key_here
   ```

## 🎯 Features

### Voice-to-Text Analysis Mode
- ✅ Real-time speech recognition
- ✅ Speaker identification (Doctor vs Patient)
- ✅ AI-powered conversation analysis
- ✅ Medical summary generation
- ✅ Timeline view of conversation

### Pure Voice Recording Mode
- ✅ High-quality audio recording
- ✅ MP3 file generation
- ✅ Download functionality
- ✅ Recording management
- ✅ File cleanup utilities

## 🌐 Access URLs

- **Backend API:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs
- **Main Page:** http://localhost:8501
- **Voice-to-Text:** http://localhost:8502
- **Voice Recorder:** http://localhost:8503

## 📋 API Endpoints

### Voice-to-Text Endpoints
- `POST /transcribe/` - Simple transcription
- `POST /analyze-conversation/` - Speaker analysis
- `POST /summarize-conversation/` - Generate summary

### Voice Recording Endpoints
- `POST /record-voice/` - Upload and convert to MP3
- `GET /download/{filename}` - Download MP3 file
- `GET /recordings` - List all recordings
- `DELETE /recordings/{filename}` - Delete recording

## 🛠️ Technology Stack

**Backend:**
- FastAPI
- Groq Whisper API
- Pydub (Audio processing)
- Python

**Frontend:**
- Streamlit
- HTML/CSS/JavaScript
- Web Audio API

## 📝 Usage Instructions

1. **Start the backend server first**
2. **Choose your mode:**
   - Use Main Page for navigation between modes
   - Or directly access specific apps
3. **For Voice-to-Text:** Record conversation and get analysis
4. **For Voice Recording:** Record audio and download MP3

## 🔒 Requirements

- Python 3.8+
- Microphone access
- Internet connection (for Groq API)
- Modern web browser

## 🆘 Troubleshooting

- **Microphone not working:** Check browser permissions
- **Backend errors:** Ensure GROQ_API_KEY is set
- **Port conflicts:** Change ports in commands if needed
- **Dependencies:** Run installation option in runner script
