# 🏥 Medical Voice Assistant

A comprehensive medical voice application with two main modes:
1. **Voice-to-Text Analysis** - Real-time transcription, speaker identification, and AI summaries
2. **Pure Voice Recording** - Simple audio recording and MP3 generation

## 🚀 Quick Start

### Option 1: Use the Runner Script (Recommended)
```bash
python run_app.py
```
This will show you a menu to start different parts of the application.

### Option 2: Manual Commands

#### 1. Start Backend Server
```bash
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 2. Start Frontend Application

**Complete Integrated App (Recommended):**
```bash
cd frontend
streamlit run medical_voice_app.py --server.port 8501
```

**Individual Components (Optional):**
```bash
# Voice-to-Text Only
cd frontend
streamlit run streamlit_app.py --server.port 8502

# Voice Recorder Only
cd frontend
streamlit run voice_recorder.py --server.port 8503
```

## 📁 Project Structure

```
Baby/
├── backend/
│   ├── main.py                    # FastAPI routes only
│   ├── voice_to_text_service.py   # Voice-to-text logic
│   ├── voice_recording_service.py # Pure voice recording logic
│   ├── groq_whisper.py           # Whisper API integration
│   ├── requirements.txt          # Backend dependencies
│   └── recordings/               # Saved MP3 files
├── frontend/
│   ├── medical_voice_app.py      # Complete integrated app (MAIN)
│   ├── main_page.py              # Navigation page (standalone)
│   ├── streamlit_app.py          # Voice-to-text interface (standalone)
│   └── voice_recorder.py         # Voice recording interface (standalone)
├── run_app.py                    # Application runner script
└── README.md                     # This file
```

## 🔧 Installation

1. **Install Dependencies:**
   ```bash
   python run_app.py
   # Choose option 5 to install dependencies
   ```

2. **Set up Environment Variables:**
   Create a `.env` file in the `backend/` directory:
   ```
   GROQ_API_KEY=your_groq_api_key_here
   ```

## 🎯 Features

### Voice-to-Text Analysis Mode
- ✅ Real-time speech recognition
- ✅ Speaker identification (Doctor vs Patient)
- ✅ AI-powered conversation analysis
- ✅ Medical summary generation
- ✅ Timeline view of conversation

### Pure Voice Recording Mode
- ✅ High-quality audio recording
- ✅ MP3 file generation
- ✅ Download functionality
- ✅ Recording management
- ✅ File cleanup utilities

## 🌐 Access URLs

- **Backend API:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs
- **Complete App:** http://localhost:8501 (Recommended - includes everything)
- **Voice-to-Text Only:** http://localhost:8502 (Optional)
- **Voice Recorder Only:** http://localhost:8503 (Optional)

## 📋 API Endpoints

### Voice-to-Text Endpoints
- `POST /transcribe/` - Simple transcription
- `POST /analyze-conversation/` - Speaker analysis
- `POST /summarize-conversation/` - Generate summary

### Voice Recording Endpoints
- `POST /record-voice/` - Upload and convert to MP3
- `GET /download/{filename}` - Download MP3 file
- `GET /recordings` - List all recordings
- `DELETE /recordings/{filename}` - Delete recording

## 🛠️ Technology Stack

**Backend:**
- FastAPI
- Groq Whisper API
- Pydub (Audio processing)
- Python

**Frontend:**
- Streamlit
- HTML/CSS/JavaScript
- Web Audio API

## 📝 Usage Instructions

1. **Start the backend server first** (Option 1 in runner script)
2. **Start the complete app** (Option 2 in runner script - Recommended)
3. **Navigate between features:**
   - Click "🎙️ Start Voice Analysis" for conversation analysis
   - Click "🎵 Start Voice Recording" for pure audio recording
   - Use "← Back to Main" to return to the main menu
4. **Everything works in one integrated application!**

## 🔒 Requirements

- Python 3.8+
- Microphone access
- Internet connection (for Groq API)
- Modern web browser

## 🆘 Troubleshooting

- **Microphone not working:** Check browser permissions
- **Backend errors:** Ensure GROQ_API_KEY is set
- **Port conflicts:** Change ports in commands if needed
- **Dependencies:** Run installation option in runner script
