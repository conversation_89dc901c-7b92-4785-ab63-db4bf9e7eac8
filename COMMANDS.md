# 🚀 Simple Commands

## Backend Command:
```bash
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## Frontend Command:
```bash
cd frontend
streamlit run medical_voice_app.py --server.port 8501
```

## Setup (One Time):
```bash
# Install backend dependencies
cd backend
pip install -r requirements.txt

# Install frontend dependencies
pip install streamlit

# Create .env file in backend folder with:
GROQ_API_KEY=your_api_key_here
```

## Access:
- Frontend: http://localhost:8501
- Backend API: http://localhost:8000
