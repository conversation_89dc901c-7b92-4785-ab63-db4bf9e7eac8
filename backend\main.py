import os
import logging
from datetime import datetime
from dotenv import load_dotenv
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

# Import our service modules
from voice_to_text_service import (
    transcribe_simple_audio,
    process_conversation_audio,
    generate_conversation_summary
)
from voice_recording_service import (
    process_voice_recording,
    get_recording_info,
    list_all_recordings,
    delete_recording,
    cleanup_old_recordings
)

# Setup simple logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# Pydantic models
class SummaryRequest(BaseModel):
    conversation: str

# Load environment variables from .env file
load_dotenv()

app = FastAPI(title="Medical Voice Assistant API", version="2.0.0")

# Allow CORS for local Streamlit frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for downloads
app.mount("/static", StaticFiles(directory="recordings"), name="static")

print("🏥 Medical Voice Assistant Backend Started!")
print("🚀 Server running on: http://localhost:8000")
print("📋 Available endpoints:")
print("   - GET  /                      : Health check")
print("   - POST /transcribe            : Simple transcription")
print("   - POST /analyze-conversation  : AI conversation analysis")
print("   - POST /summarize-conversation: Generate conversation summary")
print("   - POST /record-voice          : Pure voice recording")
print("   - GET  /download/{filename}   : Download recorded files")
print("   - GET  /recordings            : List all recordings")
print("   - DELETE /recordings/{filename}: Delete recording")
print("=" * 60)

# ============================================================================
# HEALTH CHECK ENDPOINT
# ============================================================================

@app.get("/")
def root():
    logger.info(" [HEALTH] Health check requested")
    return {
        "message": "Medical Voice Assistant API running",
        "status": "healthy",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat()
    }

# ============================================================================
# VOICE-TO-TEXT ENDPOINTS
# ============================================================================

@app.post("/transcribe/")
async def transcribe(file: UploadFile = File(...)):
    """Simple transcription without speaker analysis"""
    logger.info(f" [TRANSCRIBE] Received file: {file.filename}")

    temp_path = f"temp_{file.filename}"
    try:
        with open(temp_path, "wb") as f:
            f.write(await file.read())

        logger.info(f" [TRANSCRIBE] File saved temporarily: {temp_path}")

        result = await transcribe_simple_audio(temp_path)

        os.remove(temp_path)
        logger.info(" [TRANSCRIBE] Temporary file cleaned up")

        if "error" in result:
            return JSONResponse(result, status_code=500)
        return JSONResponse(result)

    except Exception as e:
        logger.error(f" [TRANSCRIBE] Error: {str(e)}")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return JSONResponse({
            "error": str(e),
            "transcript": "",
        }, status_code=500)

@app.post("/analyze-conversation/")
async def analyze_conversation(audio: UploadFile = File(...)):
    """Analyze conversation with speaker identification"""
    logger.info(f" [ANALYZE] Received conversation file: {audio.filename}")

    temp_path = f"temp_conversation_{audio.filename}"
    try:
        with open(temp_path, "wb") as f:
            f.write(await audio.read())

        logger.info(f" [ANALYZE] File saved temporarily: {temp_path}")

        result = await process_conversation_audio(temp_path)

        os.remove(temp_path)
        logger.info(" [ANALYZE] Temporary file cleaned up")

        if "error" in result:
            return JSONResponse(result, status_code=500)
        return JSONResponse(result)

    except Exception as e:
        logger.error(f" [ANALYZE] Error: {str(e)}")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return JSONResponse({
            "error": str(e),
            "transcript": "",
            "doctor_transcript": "",
            "patient_transcript": "",
            "full_conversation": []
        }, status_code=500)

@app.post("/generate-summary/")
async def generate_summary(request: SummaryRequest):
    """Generate summary from conversation text"""
    return await generate_conversation_summary(request.dict())

@app.post("/summarize-conversation/")
async def summarize_conversation(data: dict):
    """Generate summary from conversation data"""
    return await generate_conversation_summary(data)

# ============================================================================
# PURE VOICE RECORDING ENDPOINTS
# ============================================================================

@app.post("/record-voice/")
async def record_voice(audio: UploadFile = File(...)):
    """Record and save voice as MP3"""
    logger.info(f" [VOICE] Received voice recording: {audio.filename}")

    temp_path = f"temp_voice_{audio.filename}"
    try:
        with open(temp_path, "wb") as f:
            f.write(await audio.read())

        logger.info(f" [VOICE] File saved temporarily: {temp_path}")

        result = await process_voice_recording(temp_path, audio.filename)

        os.remove(temp_path)
        logger.info(" [VOICE] Temporary file cleaned up")

        if not result.get("success"):
            return JSONResponse(result, status_code=500)
        return JSONResponse(result)

    except Exception as e:
        logger.error(f" [VOICE] Error: {str(e)}")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return JSONResponse({
            "error": str(e),
            "success": False
        }, status_code=500)

@app.get("/download/{filename}")
async def download_file(filename: str):
    """Download recorded MP3 file"""
    file_path = os.path.join("recordings", filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='audio/mpeg'
    )

@app.get("/recordings")
async def list_recordings():
    """List all available recordings"""
    return list_all_recordings()

@app.get("/recordings/{filename}")
async def get_recording_details(filename: str):
    """Get details about a specific recording"""
    return get_recording_info(filename)

@app.delete("/recordings/{filename}")
async def delete_recording_file(filename: str):
    """Delete a specific recording"""
    return delete_recording(filename)

@app.post("/cleanup-recordings/")
async def cleanup_old_recordings_endpoint(days_old: int = 30):
    """Clean up recordings older than specified days"""
    return cleanup_old_recordings(days_old)
