import streamlit as st
import streamlit.components.v1 as components

st.set_page_config(page_title="Pure Voice Recorder", layout="wide")

st.markdown("""
    <h1 style='text-align: center; color: #90caf9;'>🎵 Pure Voice Recorder</h1>
    <p style='text-align: center; font-size: 18px; color: #b0bec5;'>Record high-quality audio and save as MP3 files</p>
""", unsafe_allow_html=True)

components.html("""
<html>
<head>
<style>
    * { margin: 0; padding: 0; box-sizing: border-box; }

    body {
        font-family: 'Segoe UI', sans-serif;
        background: #0f0f0f;
        background-image: radial-gradient(#212121 1px, transparent 1px),
                          radial-gradient(#212121 1px, transparent 1px);
        background-size: 40px 40px;
        background-position: 0 0, 20px 20px;
        animation: moveBackground 60s linear infinite;
        padding: 30px;
        color: #e0e0e0;
    }

    @keyframes moveBackground {
        from { background-position: 0 0, 20px 20px; }
        to { background-position: 1000px 1000px, 1020px 1020px; }
    }

    .container {
        max-width: 800px;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 30px;
    }

    .recorder-panel {
        border-radius: 20px;
        backdrop-filter: blur(12px);
        background: rgba(38, 50, 56, 0.6);
        box-shadow: 0 8px 25px rgba(0,0,0,0.5);
        padding: 40px;
        text-align: center;
        width: 100%;
        max-width: 600px;
    }

    .neon-title {
        text-align: center;
        font-size: 36px;
        font-weight: 800;
        margin-bottom: 10px;
        background: linear-gradient(90deg, #2979ff, #00e5ff, #2979ff);
        background-size: 300%;
        color: transparent;
        -webkit-background-clip: text;
        background-clip: text;
        animation: glowMove 5s infinite linear;
        text-shadow: 0 0 10px #2979ff99, 0 0 20px #00e5ff55;
    }

    @keyframes glowMove {
        0% { background-position: 0%; }
        100% { background-position: 300%; }
    }

    .subtext {
        text-align: center;
        font-size: 18px;
        color: #b0bec5;
        margin-bottom: 30px;
    }

    .button-row {
        display: flex;
        gap: 20px;
        margin: 30px 0;
        justify-content: center;
    }

    .btn {
        padding: 15px 30px;
        font-size: 16px;
        border: none;
        border-radius: 30px;
        cursor: pointer;
        font-weight: bold;
        width: 180px;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    .btn-start { 
        background: linear-gradient(135deg, #2979ff 0%, #1976d2 100%);
        color: white; 
    }
    
    .btn-stop { 
        background: linear-gradient(135deg, #ff1744 0%, #d32f2f 100%);
        color: white; 
    }
    
    .btn-download { 
        background: linear-gradient(135deg, #00e676 0%, #00c853 100%);
        color: #000; 
        margin-top: 20px;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.5);
        opacity: 0.95;
    }

    .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .status-indicator {
        margin: 20px 0;
        padding: 15px 25px;
        background: rgba(41, 121, 255, 0.15);
        border: 1px solid #2979ff;
        border-radius: 30px;
        font-weight: 500;
        font-size: 16px;
        color: #90caf9;
    }

    .recording-info {
        margin-top: 20px;
        padding: 20px;
        background: rgba(0, 230, 118, 0.1);
        border-left: 5px solid #00e676;
        border-radius: 10px;
        color: #c8e6c9;
    }

    .timer {
        font-size: 24px;
        font-weight: bold;
        color: #ff1744;
        margin: 15px 0;
    }

    .back-button {
        position: absolute;
        top: 20px;
        left: 20px;
        background: rgba(38, 50, 56, 0.8);
        color: #90caf9;
        border: 1px solid #90caf9;
        padding: 10px 20px;
        border-radius: 20px;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .back-button:hover {
        background: rgba(144, 202, 249, 0.2);
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .button-row {
            flex-direction: column;
            align-items: center;
        }
        
        .btn {
            width: 200px;
        }
    }
</style>
</head>
<body>
    <a href="?" class="back-button">← Back to Main</a>
    
    <div class="container">
        <div class="recorder-panel">
            <div class="neon-title">🎵 Pure Voice Recorder</div>
            <div class="subtext">Record high-quality audio and save as MP3</div>
            
            <div class="button-row">
                <button class="btn btn-start" id="startBtn">🎙️ Start Recording</button>
                <button class="btn btn-stop" id="stopBtn" disabled>⏹️ Stop Recording</button>
            </div>
            
            <div class="status-indicator" id="status">Ready to record...</div>
            <div class="timer" id="timer" style="display: none;">00:00</div>
            
            <div id="downloadSection" style="display: none;">
                <button class="btn btn-download" id="downloadBtn">📥 Download MP3</button>
                <div class="recording-info">
                    <strong>Recording completed!</strong><br>
                    Click the download button to save your MP3 file.
                </div>
            </div>
        </div>
    </div>

<script>
let mediaRecorder;
let audioChunks = [];
let isRecording = false;
let startTime;
let timerInterval;

// Add click event listeners
document.getElementById('startBtn').addEventListener('click', startRecording);
document.getElementById('stopBtn').addEventListener('click', stopRecording);

async function startRecording() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                sampleRate: 44100
            } 
        });

        mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'audio/webm;codecs=opus'
        });
        audioChunks = [];

        mediaRecorder.ondataavailable = event => {
            if (event.data.size > 0) {
                audioChunks.push(event.data);
            }
        };

        mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            await processAudio(audioBlob);
        };

        mediaRecorder.start(1000); // Collect data every second
        isRecording = true;
        startTime = Date.now();

        // Start timer
        document.getElementById('timer').style.display = 'block';
        timerInterval = setInterval(updateTimer, 1000);

        // Update UI
        document.getElementById('startBtn').disabled = true;
        document.getElementById('stopBtn').disabled = false;
        document.getElementById('status').textContent = '🔴 Recording in progress...';
        document.getElementById('downloadSection').style.display = 'none';

    } catch (error) {
        console.error('Error starting recording:', error);
        alert('Error accessing microphone. Please check permissions.');
    }
}

function stopRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        mediaRecorder.stream.getTracks().forEach(track => track.stop());
        isRecording = false;

        // Stop timer
        clearInterval(timerInterval);

        // Update UI
        document.getElementById('startBtn').disabled = false;
        document.getElementById('stopBtn').disabled = true;
        document.getElementById('status').textContent = '⏳ Processing audio...';
    }
}

function updateTimer() {
    if (isRecording) {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        document.getElementById('timer').textContent = 
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
}

async function processAudio(audioBlob) {
    try {
        const formData = new FormData();
        formData.append('audio', audioBlob, 'recording.webm');

        const response = await fetch('http://localhost:8000/record-voice/', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.download_url) {
            // Show download section
            document.getElementById('downloadSection').style.display = 'block';
            document.getElementById('downloadBtn').onclick = () => {
                window.open(data.download_url, '_blank');
            };
            document.getElementById('status').textContent = '✅ Recording ready for download!';
        } else {
            throw new Error('No download URL received');
        }

    } catch (error) {
        console.error('Error processing audio:', error);
        document.getElementById('status').textContent = '❌ Error processing audio - Ready to try again';
        alert('Error processing audio. Please check if the backend server is running.');
    }
}
</script>
</body>
</html>
""", height=700, scrolling=True)
