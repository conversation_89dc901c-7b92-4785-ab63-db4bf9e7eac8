import streamlit as st
import streamlit.components.v1 as components

# Configure the page
st.set_page_config(
    page_title="Medical Voice Assistant",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Initialize session state
if 'current_page' not in st.session_state:
    st.session_state.current_page = 'main'

def show_main_page():
    """Display the main navigation page"""
    
    # Custom CSS for the main page
    st.markdown("""
    <style>
        .main-container {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
            min-height: 100vh;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .app-title {
            font-size: 3.5rem;
            font-weight: 800;
            text-align: center;
            margin-bottom: 1rem;
            background: linear-gradient(90deg, #00e5ff, #7c4dff, #00e5ff);
            background-size: 300%;
            color: transparent;
            -webkit-background-clip: text;
            background-clip: text;
            animation: glowMove 5s infinite linear;
            text-shadow: 0 0 20px #00e5ff99;
        }
        
        @keyframes glowMove {
            0% { background-position: 0%; }
            100% { background-position: 300%; }
        }
        
        .app-subtitle {
            font-size: 1.3rem;
            color: #b0bec5;
            text-align: center;
            margin-bottom: 3rem;
            font-weight: 300;
        }
        
        .buttons-container {
            display: flex;
            gap: 3rem;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        
        .feature-card {
            background: rgba(38, 50, 56, 0.8);
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            backdrop-filter: blur(15px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.6);
            transition: all 0.4s ease;
            border: 1px solid rgba(255,255,255,0.1);
            min-width: 300px;
            max-width: 350px;
        }
        
        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.8);
            border-color: rgba(255,255,255,0.2);
        }
        
        .feature-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            display: block;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 1rem;
        }
        
        .feature-description {
            font-size: 1rem;
            color: #b0bec5;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .buttons-container {
                flex-direction: column;
                gap: 2rem;
            }
            
            .feature-card {
                min-width: 280px;
                max-width: 320px;
            }
            
            .app-title {
                font-size: 2.5rem;
            }
        }
    </style>
    """, unsafe_allow_html=True)

    # Main page content
    st.markdown("""
    <div class="main-container">
        <h1 class="app-title">🏥 Medical Voice Assistant</h1>
        <p class="app-subtitle">Choose your preferred voice recording mode</p>
        
        <div class="buttons-container">
            <div class="feature-card">
                <span class="feature-icon">🎙️💬</span>
                <h3 class="feature-title">Voice-to-Text Analysis</h3>
                <p class="feature-description">
                    Record conversations, get real-time transcription, speaker identification, 
                    and AI-powered medical summaries with SOAP notes.
                </p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">🎵📁</span>
                <h3 class="feature-title">Pure Voice Recording</h3>
                <p class="feature-description">
                    Simple audio recording mode that captures high-quality voice recordings 
                    and saves them as MP3 files for future use.
                </p>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Navigation buttons
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col1:
        if st.button("🎙️💬 Start Voice Analysis", key="voice_analysis", use_container_width=True):
            st.session_state.current_page = 'voice_to_text'
            st.experimental_rerun()
    
    with col2:
        if st.button("🎵📁 Start Voice Recording", key="voice_recording", use_container_width=True):
            st.session_state.current_page = 'voice_only'
            st.experimental_rerun()
    
    with col3:
        if st.button("🔄 Refresh", key="refresh", use_container_width=True):
            st.experimental_rerun()

def show_voice_to_text_page():
    """Display the voice-to-text analysis page"""
    
    # Back button
    if st.button("← Back to Main", key="back_to_main_1"):
        st.session_state.current_page = 'main'
        st.experimental_rerun()
    
    st.markdown("""
        <h1 style='text-align: center; color: #90caf9;'> Doctor-Patient Voice Conversation Analyzer</h1>
        <p style='text-align: center; font-size: 18px; color: #b0bec5;'>Record medical conversations, separate speakers, and get AI-based summaries</p>
    """, unsafe_allow_html=True)

    # Include the voice-to-text functionality
    components.html(open('streamlit_app.py').read().split('components.html("""')[1].split('""", height=900')[0], height=900, scrolling=True)

def show_voice_recorder_page():
    """Display the pure voice recording page"""
    
    # Back button
    if st.button("← Back to Main", key="back_to_main_2"):
        st.session_state.current_page = 'main'
        st.experimental_rerun()
    
    st.markdown("""
        <h1 style='text-align: center; color: #90caf9;'>🎵 Pure Voice Recorder</h1>
        <p style='text-align: center; font-size: 18px; color: #b0bec5;'>Record high-quality audio and save as MP3 files</p>
    """, unsafe_allow_html=True)

    # Include the voice recorder functionality
    components.html(open('voice_recorder.py').read().split('components.html("""')[1].split('""", height=700')[0], height=700, scrolling=True)

# Main app logic
def main():
    # Show different pages based on current page
    if st.session_state.current_page == 'main':
        show_main_page()
    elif st.session_state.current_page == 'voice_to_text':
        show_voice_to_text_page()
    elif st.session_state.current_page == 'voice_only':
        show_voice_recorder_page()

if __name__ == "__main__":
    main()
