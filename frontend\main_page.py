import streamlit as st
import streamlit.components.v1 as components

st.set_page_config(page_title="Medical Voice App", layout="wide")

# Custom CSS for the main page
st.markdown("""
<style>
    .main-container {
        background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
        min-height: 100vh;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
    .app-title {
        font-size: 3.5rem;
        font-weight: 800;
        text-align: center;
        margin-bottom: 1rem;
        background: linear-gradient(90deg, #00e5ff, #7c4dff, #00e5ff);
        background-size: 300%;
        color: transparent;
        -webkit-background-clip: text;
        background-clip: text;
        animation: glowMove 5s infinite linear;
        text-shadow: 0 0 20px #00e5ff99;
    }
    
    @keyframes glowMove {
        0% { background-position: 0%; }
        100% { background-position: 300%; }
    }
    
    .app-subtitle {
        font-size: 1.3rem;
        color: #b0bec5;
        text-align: center;
        margin-bottom: 3rem;
        font-weight: 300;
    }
    
    .buttons-container {
        display: flex;
        gap: 3rem;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 2rem;
    }
    
    .feature-card {
        background: rgba(38, 50, 56, 0.8);
        border-radius: 20px;
        padding: 2.5rem;
        text-align: center;
        backdrop-filter: blur(15px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.6);
        transition: all 0.4s ease;
        border: 1px solid rgba(255,255,255,0.1);
        min-width: 300px;
        max-width: 350px;
    }
    
    .feature-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0,0,0,0.8);
        border-color: rgba(255,255,255,0.2);
    }
    
    .feature-icon {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        display: block;
    }
    
    .feature-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 1rem;
    }
    
    .feature-description {
        font-size: 1rem;
        color: #b0bec5;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .feature-button {
        background: linear-gradient(135deg, #00e676 0%, #00c853 100%);
        color: #000;
        border: none;
        padding: 1rem 2rem;
        border-radius: 50px;
        font-size: 1.1rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        box-shadow: 0 8px 25px rgba(0, 230, 118, 0.3);
    }
    
    .feature-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(0, 230, 118, 0.5);
        background: linear-gradient(135deg, #00c853 0%, #00e676 100%);
    }
    
    .feature-button.voice-only {
        background: linear-gradient(135deg, #2979ff 0%, #1976d2 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(41, 121, 255, 0.3);
    }
    
    .feature-button.voice-only:hover {
        background: linear-gradient(135deg, #1976d2 0%, #2979ff 100%);
        box-shadow: 0 12px 35px rgba(41, 121, 255, 0.5);
    }
    
    @media (max-width: 768px) {
        .buttons-container {
            flex-direction: column;
            gap: 2rem;
        }
        
        .feature-card {
            min-width: 280px;
            max-width: 320px;
        }
        
        .app-title {
            font-size: 2.5rem;
        }
    }
</style>
""", unsafe_allow_html=True)

# Main page content
st.markdown("""
<div class="main-container">
    <h1 class="app-title">🏥 Medical Voice Assistant</h1>
    <p class="app-subtitle">Choose your preferred voice recording mode</p>
    
    <div class="buttons-container">
        <div class="feature-card">
            <span class="feature-icon">🎙️💬</span>
            <h3 class="feature-title">Voice-to-Text Analysis</h3>
            <p class="feature-description">
                Record conversations, get real-time transcription, speaker identification, 
                and AI-powered medical summaries with SOAP notes.
            </p>
            <button class="feature-button" onclick="window.location.href='streamlit_app.py'">
                Start Voice Analysis
            </button>
        </div>
        
        <div class="feature-card">
            <span class="feature-icon">🎵📁</span>
            <h3 class="feature-title">Pure Voice Recording</h3>
            <p class="feature-description">
                Simple audio recording mode that captures high-quality voice recordings 
                and saves them as MP3 files for future use.
            </p>
            <button class="feature-button voice-only" onclick="window.location.href='voice_recorder.py'">
                Start Voice Recording
            </button>
        </div>
    </div>
</div>
""", unsafe_allow_html=True)

# Note: Navigation is handled by JavaScript buttons above
