#!/usr/bin/env python3
"""
Medical Voice Assistant - Complete App Starter
This script starts both backend and frontend automatically
"""

import subprocess
import sys
import os
import time
import threading

def start_backend():
    """Start the FastAPI backend server"""
    print("🚀 Starting Backend Server...")
    os.chdir("backend")
    try:
        subprocess.run([sys.executable, "-m", "uvicorn", "main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"])
    except Exception as e:
        print(f"❌ Backend error: {e}")
    finally:
        os.chdir("..")

def start_frontend():
    """Start the Streamlit frontend app"""
    print("🎨 Starting Frontend App...")
    time.sleep(3)  # Wait for backend to start
    os.chdir("frontend")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "medical_voice_app.py", "--server.port", "8501"])
    except Exception as e:
        print(f"❌ Frontend error: {e}")
    finally:
        os.chdir("..")

def main():
    print("=" * 60)
    print("🏥 MEDICAL VOICE ASSISTANT - COMPLETE STARTUP")
    print("=" * 60)
    print("🚀 Starting both Backend and Frontend...")
    print("📍 Backend will be at: http://localhost:8000")
    print("📍 Frontend will be at: http://localhost:8501")
    print("⏳ Please wait while everything starts up...")
    print("=" * 60)
    print()

    # Start backend in a separate thread
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    backend_thread.start()
    
    # Start frontend in main thread
    start_frontend()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Shutting down Medical Voice Assistant...")
        print("✅ Thank you for using our app!")
