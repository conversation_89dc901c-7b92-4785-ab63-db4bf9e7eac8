#!/usr/bin/env python3
"""
Medical Voice Assistant - Application Runner
This script helps you run different parts of the application
"""

import subprocess
import sys
import os
import time

def print_banner():
    print("=" * 60)
    print("🏥 MEDICAL VOICE ASSISTANT")
    print("=" * 60)
    print("Choose what you want to run:")
    print()
    print("1. 🚀 Start Backend Server (FastAPI)")
    print("2. 🎨 Start Complete App (Streamlit - Recommended)")
    print("3. 🎙️ Start Voice-to-Text Only (Streamlit)")
    print("4. 🎵 Start Voice Recorder Only (Streamlit)")
    print("5. 🔧 Install Dependencies")
    print("6. ❌ Exit")
    print("=" * 60)

def run_backend():
    print("🚀 Starting Backend Server...")
    print("📍 Location: http://localhost:8000")
    print("📋 API Docs: http://localhost:8000/docs")
    print()
    
    os.chdir("backend")
    try:
        subprocess.run([sys.executable, "-m", "uvicorn", "main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"])
    except KeyboardInterrupt:
        print("\n🛑 Backend server stopped")
    finally:
        os.chdir("..")

def run_complete_app():
    print("🎨 Starting Complete Medical Voice App...")
    print("📍 Location: http://localhost:8501")
    print("✨ This includes both Voice-to-Text and Voice Recording features!")
    print()

    os.chdir("frontend")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "medical_voice_app.py", "--server.port", "8501"])
    except KeyboardInterrupt:
        print("\n🛑 Complete app stopped")
    finally:
        os.chdir("..")

def run_voice_to_text():
    print("🎙️ Starting Voice-to-Text App...")
    print("📍 Location: http://localhost:8502")
    print()
    
    os.chdir("frontend")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "streamlit_app.py", "--server.port", "8502"])
    except KeyboardInterrupt:
        print("\n🛑 Voice-to-Text app stopped")
    finally:
        os.chdir("..")

def run_voice_recorder():
    print("🎵 Starting Voice Recorder App...")
    print("📍 Location: http://localhost:8503")
    print()
    
    os.chdir("frontend")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "voice_recorder.py", "--server.port", "8503"])
    except KeyboardInterrupt:
        print("\n🛑 Voice Recorder app stopped")
    finally:
        os.chdir("..")

def install_dependencies():
    print("🔧 Installing Dependencies...")
    print()
    
    print("📦 Installing Backend Dependencies...")
    os.chdir("backend")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    os.chdir("..")
    
    print("📦 Installing Frontend Dependencies...")
    subprocess.run([sys.executable, "-m", "pip", "install", "streamlit"])
    
    print("✅ Dependencies installed successfully!")
    print()

def main():
    while True:
        print_banner()
        
        try:
            choice = input("Enter your choice (1-6): ").strip()
            
            if choice == "1":
                run_backend()
            elif choice == "2":
                run_complete_app()
            elif choice == "3":
                run_voice_to_text()
            elif choice == "4":
                run_voice_recorder()
            elif choice == "5":
                install_dependencies()
            elif choice == "6":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-6.")
                time.sleep(2)
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            time.sleep(2)

if __name__ == "__main__":
    main()
