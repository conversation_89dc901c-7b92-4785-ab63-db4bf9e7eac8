import streamlit as st
import streamlit.components.v1 as components

# Configure the page
st.set_page_config(
    page_title="Medical Voice Assistant",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Initialize session state
if 'current_page' not in st.session_state:
    st.session_state.current_page = 'main'

def show_main_page():
    """Display the main navigation page"""
    
    st.markdown("""
    <style>
        .main-title {
            font-size: 3rem;
            font-weight: 800;
            text-align: center;
            margin-bottom: 1rem;
            background: linear-gradient(90deg, #00e5ff, #7c4dff, #00e5ff);
            background-size: 300%;
            color: transparent;
            -webkit-background-clip: text;
            background-clip: text;
            animation: glowMove 5s infinite linear;
        }
        
        @keyframes glowMove {
            0% { background-position: 0%; }
            100% { background-position: 300%; }
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #b0bec5;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .feature-card {
            background: rgba(38, 50, 56, 0.8);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin: 1rem 0;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }
        
        .feature-description {
            color: #b0bec5;
            margin-bottom: 1rem;
        }
    </style>
    """, unsafe_allow_html=True)

    st.markdown('<h1 class="main-title">🏥 Medical Voice Assistant</h1>', unsafe_allow_html=True)
    st.markdown('<p class="subtitle">Choose your preferred voice recording mode</p>', unsafe_allow_html=True)

    # Create two columns for the feature cards
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">🎙️💬</div>
            <h3 class="feature-title">Voice-to-Text Analysis</h3>
            <p class="feature-description">
                Record conversations, get real-time transcription, speaker identification, 
                and AI-powered medical summaries.
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("🎙️ Start Voice Analysis", key="voice_analysis", use_container_width=True):
            st.session_state.current_page = 'voice_to_text'
            st.experimental_rerun()
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">🎵📁</div>
            <h3 class="feature-title">Pure Voice Recording</h3>
            <p class="feature-description">
                Simple audio recording mode that captures high-quality voice recordings 
                and saves them as MP3 files.
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("🎵 Start Voice Recording", key="voice_recording", use_container_width=True):
            st.session_state.current_page = 'voice_only'
            st.experimental_rerun()

def show_voice_to_text_page():
    """Display the voice-to-text analysis page"""
    
    # Back button
    if st.button("← Back to Main", key="back_to_main_1"):
        st.session_state.current_page = 'main'
        st.experimental_rerun()
    
    st.markdown("""
        <h1 style='text-align: center; color: #90caf9;'>🎙️ Doctor-Patient Voice Conversation Analyzer</h1>
        <p style='text-align: center; font-size: 18px; color: #b0bec5;'>Record medical conversations, separate speakers, and get AI-based summaries</p>
    """, unsafe_allow_html=True)

    # Voice-to-text functionality
    components.html("""
<html>
<head>
<style>
    * { margin: 0; padding: 0; box-sizing: border-box; }

    body {
        font-family: 'Segoe UI', sans-serif;
        background: #0f0f0f;
        background-image: radial-gradient(#212121 1px, transparent 1px),
                          radial-gradient(#212121 1px, transparent 1px);
        background-size: 40px 40px;
        background-position: 0 0, 20px 20px;
        animation: moveBackground 60s linear infinite;
        padding: 30px;
        color: #e0e0e0;
    }

    @keyframes moveBackground {
        from { background-position: 0 0, 20px 20px; }
        to { background-position: 1000px 1000px, 1020px 1020px; }
    }

    .container {
        max-width: 100%;
        margin: auto;
        display: flex;
        flex-direction: row;
        gap: 30px;
        align-items: flex-start;
    }

    .left-panel, .right-panel {
        border-radius: 20px;
        backdrop-filter: blur(12px);
        background: rgba(38, 50, 56, 0.6);
        box-shadow: 0 8px 25px rgba(0,0,0,0.5);
        padding: 30px;
    }

    .left-panel {
        flex: 0.3;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .right-panel {
        flex: 0.7;
        max-height: 90vh;
        overflow-y: auto;
    }

    .button-row {
        display: flex;
        gap: 15px;
        margin-top: 20px;
    }

    .btn {
        padding: 12px 25px;
        font-size: 15px;
        border: none;
        border-radius: 30px;
        cursor: pointer;
        font-weight: bold;
        width: 180px;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    .btn-start { background-color: #00e676; color: #000; }
    .btn-stop { background-color: #ff1744; color: white; }
    .btn-summary { margin-top: 20px; background: #2979ff; color: white; }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.5);
        opacity: 0.95;
    }

    .status-indicator {
        margin-top: 20px;
        padding: 12px 20px;
        background: rgba(25, 118, 210, 0.15);
        border: 1px solid #90caf9;
        border-radius: 30px;
        font-weight: 500;
        font-size: 14px;
        color: #90caf9;
    }

    .live-section {
        margin-bottom: 30px;
        background: rgba(0, 230, 118, 0.1);
        border-left: 5px solid #00e676;
        border-radius: 10px;
        padding: 15px 20px;
        color: #c8e6c9;
        animation: pulse 2s infinite ease-in-out;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0px #00e67633; }
        50% { box-shadow: 0 0 12px #00e67699; }
        100% { box-shadow: 0 0 0px #00e67633; }
    }

    .timeline-item {
        margin-top: 15px;
        padding: 15px;
        border-left: 5px solid #90caf9;
        background: rgba(55,71,79,0.6);
        border-radius: 10px;
        color: #ffffff;
        transition: all 0.3s;
    }

    .timeline-item:hover {
        transform: scale(1.01);
        background: rgba(69, 90, 100, 0.8);
    }

    .speaker-doctor { border-left-color: #00e676; }
    .speaker-patient { border-left-color: #2979ff; }

    .section-title {
        font-weight: bold;
        font-size: 20px;
        margin-top: 30px;
        color: #ffffff;
    }

    .transcript-content, .summary-result {
        background: rgba(55,71,79,0.7);
        border-radius: 10px;
        padding: 15px 20px;
        margin-top: 10px;
        color: #fff;
        box-shadow: inset 0 0 10px rgba(0,0,0,0.4);
    }

    @media (max-width: 1024px) {
        .container {
            flex-direction: column;
        }
        .left-panel, .right-panel {
            flex: 1;
            width: 100%;
        }
        .button-row {
            justify-content: center;
            flex-wrap: wrap;
        }
    }
</style>
</head>
<body>
    <div class="container">
        <div class="left-panel">
            <h2>🎙️ Start Recording</h2>
            <div class="button-row">
                <button class="btn btn-start" id="startBtn">Start</button>
                <button class="btn btn-stop" id="stopBtn" disabled>Stop</button>
            </div>
            <div class="status-indicator" id="status">Waiting to start...</div>
        </div>
        <div class="right-panel">
            <div class="live-section">
                <div><strong>Live Words:</strong> <span id="liveWords">Listening...</span></div>
            </div>
            <div id="result"></div>
        </div>
    </div>
<script>
let mediaRecorder;
let audioChunks = [];
let recognition;
let isRecording = false;

// Initialize speech recognition
if ('webkitSpeechRecognition' in window) {
    recognition = new webkitSpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';

    recognition.onresult = function(event) {
        let interimTranscript = '';
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }

        document.getElementById('liveWords').textContent =
            finalTranscript + (interimTranscript ? ' ' + interimTranscript : '');
    };

    recognition.onerror = function(event) {
        console.error('Speech recognition error:', event.error);
    };
}

// Add click event listeners
document.getElementById('startBtn').addEventListener('click', startRecording);
document.getElementById('stopBtn').addEventListener('click', stopRecording);

async function startRecording() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

        mediaRecorder = new MediaRecorder(stream);
        audioChunks = [];

        mediaRecorder.ondataavailable = event => {
            audioChunks.push(event.data);
        };

        mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            await processAudio(audioBlob);
        };

        mediaRecorder.start();
        isRecording = true;

        // Start speech recognition
        if (recognition) {
            recognition.start();
        }

        // Update UI
        document.getElementById('startBtn').disabled = true;
        document.getElementById('stopBtn').disabled = false;
        document.getElementById('status').textContent = '🔴 Recording...';

    } catch (error) {
        console.error('Error starting recording:', error);
        alert('Error accessing microphone. Please check permissions.');
    }
}

function stopRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        mediaRecorder.stream.getTracks().forEach(track => track.stop());
        isRecording = false;

        // Stop speech recognition
        if (recognition) {
            recognition.stop();
        }

        // Update UI
        document.getElementById('startBtn').disabled = false;
        document.getElementById('stopBtn').disabled = true;
        document.getElementById('status').textContent = '⏳ Processing...';
    }
}

async function processAudio(audioBlob) {
    try {
        const formData = new FormData();
        formData.append('audio', audioBlob, 'recording.wav');

        const response = await fetch('http://localhost:8000/analyze-conversation/', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        displayResults(data);

        document.getElementById('status').textContent = '✅ Done';

    } catch (error) {
        console.error('Error processing audio:', error);
        document.getElementById('status').textContent = '❌ Error - Ready to try again';
        alert('Error processing audio. Please check if the backend server is running.');
    }
}

function displayResults(data) {
    let html = ``;

    if (data.full_conversation) {
        html += `<div class="section-title">👥 Conversation Timeline</div>`;

        data.full_conversation.forEach(entry => {
            const speakerClass = entry.speaker === 'doctor' ? 'speaker-doctor' : 'speaker-patient';
            html += `
                <div class="timeline-item ${speakerClass}">
                    <strong>${entry.speaker.toUpperCase()}:</strong> ${entry.text}
                </div>
            `;
        });

        html += `<button class="btn btn-summary" onclick="generateSummary()">
                    🧠 Generate Summary
                 </button>`;
    }

    document.getElementById('result').innerHTML = html;
}

async function generateSummary() {
    try {
        const button = document.querySelector('.btn-summary');
        button.disabled = true;
        button.textContent = '⏳ Generating Summary...';

        const response = await fetch('http://localhost:8000/generate-summary/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                conversation: document.getElementById('result').textContent
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        const summaryHtml = `
            <div class="section-title">📋 AI Summary</div>
            <div class="summary-result">${data.summary}</div>
        `;

        document.getElementById('result').innerHTML += summaryHtml;

        button.disabled = false;
        button.textContent = '🧠 Generate Summary';

    } catch (error) {
        console.error('Error generating summary:', error);
        alert('Error generating summary. Please try again.');

        const button = document.querySelector('.btn-summary');
        button.disabled = false;
        button.textContent = '🧠 Generate Summary';
    }
}
</script>
</body>
</html>
""", height=800, scrolling=True)

def show_voice_recorder_page():
    """Display the pure voice recording page"""

    # Back button
    if st.button("← Back to Main", key="back_to_main_2"):
        st.session_state.current_page = 'main'
        st.experimental_rerun()

    st.markdown("""
        <h1 style='text-align: center; color: #90caf9;'>🎵 Pure Voice Recorder</h1>
        <p style='text-align: center; font-size: 18px; color: #b0bec5;'>Record high-quality audio and save as MP3 files</p>
    """, unsafe_allow_html=True)

    # Voice recorder functionality
    components.html("""
<html>
<head>
<style>
    * { margin: 0; padding: 0; box-sizing: border-box; }

    body {
        font-family: 'Segoe UI', sans-serif;
        background: #0f0f0f;
        background-image: radial-gradient(#212121 1px, transparent 1px),
                          radial-gradient(#212121 1px, transparent 1px);
        background-size: 40px 40px;
        background-position: 0 0, 20px 20px;
        animation: moveBackground 60s linear infinite;
        padding: 30px;
        color: #e0e0e0;
    }

    @keyframes moveBackground {
        from { background-position: 0 0, 20px 20px; }
        to { background-position: 1000px 1000px, 1020px 1020px; }
    }

    .container {
        max-width: 800px;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 30px;
    }

    .recorder-panel {
        border-radius: 20px;
        backdrop-filter: blur(12px);
        background: rgba(38, 50, 56, 0.6);
        box-shadow: 0 8px 25px rgba(0,0,0,0.5);
        padding: 40px;
        text-align: center;
        width: 100%;
        max-width: 600px;
    }

    .neon-title {
        text-align: center;
        font-size: 36px;
        font-weight: 800;
        margin-bottom: 10px;
        background: linear-gradient(90deg, #2979ff, #00e5ff, #2979ff);
        background-size: 300%;
        color: transparent;
        -webkit-background-clip: text;
        background-clip: text;
        animation: glowMove 5s infinite linear;
        text-shadow: 0 0 10px #2979ff99, 0 0 20px #00e5ff55;
    }

    @keyframes glowMove {
        0% { background-position: 0%; }
        100% { background-position: 300%; }
    }

    .subtext {
        text-align: center;
        font-size: 18px;
        color: #b0bec5;
        margin-bottom: 30px;
    }

    .button-row {
        display: flex;
        gap: 20px;
        margin: 30px 0;
        justify-content: center;
    }

    .btn {
        padding: 15px 30px;
        font-size: 16px;
        border: none;
        border-radius: 30px;
        cursor: pointer;
        font-weight: bold;
        width: 180px;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    .btn-start {
        background: linear-gradient(135deg, #2979ff 0%, #1976d2 100%);
        color: white;
    }

    .btn-stop {
        background: linear-gradient(135deg, #ff1744 0%, #d32f2f 100%);
        color: white;
    }

    .btn-download {
        background: linear-gradient(135deg, #00e676 0%, #00c853 100%);
        color: #000;
        margin-top: 20px;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.5);
        opacity: 0.95;
    }

    .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .status-indicator {
        margin: 20px 0;
        padding: 15px 25px;
        background: rgba(41, 121, 255, 0.15);
        border: 1px solid #2979ff;
        border-radius: 30px;
        font-weight: 500;
        font-size: 16px;
        color: #90caf9;
    }

    .recording-info {
        margin-top: 20px;
        padding: 20px;
        background: rgba(0, 230, 118, 0.1);
        border-left: 5px solid #00e676;
        border-radius: 10px;
        color: #c8e6c9;
    }

    .timer {
        font-size: 24px;
        font-weight: bold;
        color: #ff1744;
        margin: 15px 0;
    }

    @media (max-width: 768px) {
        .button-row {
            flex-direction: column;
            align-items: center;
        }

        .btn {
            width: 200px;
        }
    }
</style>
</head>
<body>
    <div class="container">
        <div class="recorder-panel">
            <div class="neon-title">🎵 Pure Voice Recorder</div>
            <div class="subtext">Record high-quality audio and save as MP3</div>

            <div class="button-row">
                <button class="btn btn-start" id="startBtn">🎙️ Start Recording</button>
                <button class="btn btn-stop" id="stopBtn" disabled>⏹️ Stop Recording</button>
            </div>

            <div class="status-indicator" id="status">Ready to record...</div>
            <div class="timer" id="timer" style="display: none;">00:00</div>

            <div id="downloadSection" style="display: none;">
                <button class="btn btn-download" id="downloadBtn">📥 Download MP3</button>
                <div class="recording-info">
                    <strong>Recording completed!</strong><br>
                    Click the download button to save your MP3 file.
                </div>
            </div>
        </div>
    </div>

<script>
let mediaRecorder;
let audioChunks = [];
let isRecording = false;
let startTime;
let timerInterval;

// Add click event listeners
document.getElementById('startBtn').addEventListener('click', startRecording);
document.getElementById('stopBtn').addEventListener('click', stopRecording);

async function startRecording() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                sampleRate: 44100
            }
        });

        mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'audio/webm;codecs=opus'
        });
        audioChunks = [];

        mediaRecorder.ondataavailable = event => {
            if (event.data.size > 0) {
                audioChunks.push(event.data);
            }
        };

        mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            await processAudio(audioBlob);
        };

        mediaRecorder.start(1000); // Collect data every second
        isRecording = true;
        startTime = Date.now();

        // Start timer
        document.getElementById('timer').style.display = 'block';
        timerInterval = setInterval(updateTimer, 1000);

        // Update UI
        document.getElementById('startBtn').disabled = true;
        document.getElementById('stopBtn').disabled = false;
        document.getElementById('status').textContent = '🔴 Recording in progress...';
        document.getElementById('downloadSection').style.display = 'none';

    } catch (error) {
        console.error('Error starting recording:', error);
        alert('Error accessing microphone. Please check permissions.');
    }
}

function stopRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        mediaRecorder.stream.getTracks().forEach(track => track.stop());
        isRecording = false;

        // Stop timer
        clearInterval(timerInterval);

        // Update UI
        document.getElementById('startBtn').disabled = false;
        document.getElementById('stopBtn').disabled = true;
        document.getElementById('status').textContent = '⏳ Processing audio...';
    }
}

function updateTimer() {
    if (isRecording) {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        document.getElementById('timer').textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
}

async function processAudio(audioBlob) {
    try {
        const formData = new FormData();
        formData.append('audio', audioBlob, 'recording.webm');

        const response = await fetch('http://localhost:8000/record-voice/', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.download_url) {
            // Show download section
            document.getElementById('downloadSection').style.display = 'block';
            document.getElementById('downloadBtn').onclick = () => {
                window.open(data.download_url, '_blank');
            };
            document.getElementById('status').textContent = '✅ Recording ready for download!';
        } else {
            throw new Error('No download URL received');
        }

    } catch (error) {
        console.error('Error processing audio:', error);
        document.getElementById('status').textContent = '❌ Error processing audio - Ready to try again';
        alert('Error processing audio. Please check if the backend server is running.');
    }
}
</script>
</body>
</html>
""", height=700, scrolling=True)

# Main app logic
def main():
    # Show different pages based on current page
    if st.session_state.current_page == 'main':
        show_main_page()
    elif st.session_state.current_page == 'voice_to_text':
        show_voice_to_text_page()
    elif st.session_state.current_page == 'voice_only':
        show_voice_recorder_page()

if __name__ == "__main__":
    main()
